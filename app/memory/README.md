# Memory Management System with LangMem

This module provides memory management capabilities for the TraderGPT application using LangMem for embeddings and Qdrant for vector storage.

## Overview

The memory system is designed to:

1. Store and retrieve conversation history with sliding window management
2. Provide relevant context for future conversations
3. Use vector search to find relevant memories
4. Automatically maintain a maximum of 15 memories per chat session (sliding window)
5. Ensure chat isolation - each chat_id maintains its own separate memory window

## Components

### Memory Manager

The memory manager provides the core functionality for memory management:

- `get_memory_store`: Get a Qdrant client instance for vector storage
- `get_embeddings`: Get embeddings for text using LangMem's utility function
- `store_user_message`: Store a user message in memory
- `search_memories`: Search for relevant memories

### Sliding Window Management

The memory system implements a sliding window approach to manage memory usage:

- **Storage Limit**: Maximum of 15 user questions per chat_id
- **Automatic Cleanup**: When storing a new message would exceed the limit, the oldest message for that chat_id is automatically deleted
- **FIFO Behavior**: Implements First In, First Out queue behavior
- **Chat Isolation**: Each chat_id maintains its own separate sliding window
- **Search Scope**: Search results are limited to the most recent 15 messages per chat_id
- **Timestamp Ordering**: Results are ordered by recency (most recent first)

The sliding window size can be configured by modifying `MAX_MEMORIES_PER_CHAT` in `langmem_config.py`.

### Time-Weighted Retrieval

The memory system implements LangChain-style time-weighted retrieval to prioritize recent memories:

- **Time Decay**: Memories get progressively lower relevance scores as they age
- **Exponential Decay Function**: Uses `exp(-decay_rate * hours_passed)` formula
- **Combined Scoring**: Combines semantic similarity with time weighting
- **Access Time Updates**: Updates `last_accessed_at` when memories are retrieved (following LangChain's approach)
- **Configurable Balance**: Control the balance between semantic similarity and recency

**Formula**: `combined_score = (1 - TIME_WEIGHT_FACTOR) * semantic_score + TIME_WEIGHT_FACTOR * time_weight`

**Configuration Options**:
- `ENABLE_TIME_WEIGHTING`: Enable/disable time-weighted scoring (default: True)
- `TIME_DECAY_RATE`: Rate of decay per hour (default: 0.01 = 1% decay per hour)
- `TIME_WEIGHT_FACTOR`: Balance between semantic and temporal factors (0.0 = only semantic, 1.0 = only time, 0.5 = balanced)

### Configuration

Memory configuration is defined in `langmem_config.py`:

- `QDRANT_URL`: URL for the Qdrant vector database
- `QDRANT_COLLECTION_NAME`: Name of the Qdrant collection for storing memories

## Integration

The memory system is integrated with the agent system in the following ways:

1. In `agent_creation.py`, the `writer_node` function searches for relevant memories and includes them in the prompt.
2. In `calling_agent.py`, the `generating_reply` function searches for relevant memories and includes them in the prompt.
3. Both functions store the conversation in memory after generating a response.

## Usage

### Storing User Messages

```python
from app.memory import store_user_message

messages = [
    {"role": "user", "content": "What do you think about Tesla stock?"},
    {"role": "assistant", "content": "Tesla is a leading electric vehicle manufacturer..."}
]

await store_user_message(
    user_id="user123",
    chat_id="chat456",
    messages=messages,
    run_async=True
)
```

### Searching Memories

```python
from app.memory import search_memories

memory_results = await search_memories(
    user_id="user123",
    query="Tesla stock",
    limit=3,
    include_conversation_history=True,
    chat_id="chat456"
)
```

### Time-Weighted Search Example

```python
from app.memory import search_memories

# Search with time weighting enabled (default)
memory_results = await search_memories(
    user_id="user123",
    query="Tesla stock",
    limit=5,
    include_conversation_history=True,
    chat_id="chat456"
)

# Results will include time-weighted scores
for result in memory_results["results"]:
    print(f"Combined Score: {result['score']:.3f}")
    print(f"Semantic Score: {result['original_score']:.3f}")
    print(f"Time Weight: {result['time_weight']:.3f}")
    print(f"Content: {result['memory']}")
```

### Testing Time-Weighted Functionality

```python
from app.memory.memory_manager import calculate_time_weight, calculate_combined_score
import datetime

# Test time weight calculation
current_time = datetime.datetime.now()
one_hour_ago = current_time - datetime.timedelta(hours=1)
time_weight = calculate_time_weight(one_hour_ago.isoformat(), current_time)

# Test combined score calculation
semantic_score = 0.8
combined_score = calculate_combined_score(semantic_score, one_hour_ago.isoformat(), current_time)
```

## Testing

Use the provided test scripts to verify the memory system functionality:

### Sliding Window Test
```bash
python test_sliding_window.py
```

### Time-Weighted Retrieval Test
```bash
python test_time_weighted.py
```

### Original Memory Test
```bash
python test_langmem_direct.py
```

## Initialization

Before using the memory system, initialize the Qdrant collection:

```python
from app.memory.init_qdrant import init_qdrant_collection

init_qdrant_collection()
```
