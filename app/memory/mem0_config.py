import os

# Mem0 configuration for direct usage with Qdrant
MEM0_CONFIG = {
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "host": os.getenv("QDRANT_HOST", "**************"),
            "port": int(os.getenv("QDRANT_PORT", "6333")),
            "collection_name": os.getenv("MEM0_COLLECTION_NAME", "mem0_memories"),
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "model": os.getenv("MEM0_EMBEDDING_MODEL", "text-embedding-3-small"),
            "api_key": os.getenv("OPENAI_API_KEY"),
        }
    },
    "llm": {
        "provider": "openai",
        "config": {
            "model": os.getenv("MEM0_LLM_MODEL", "gpt-4o-mini"),
            "api_key": os.getenv("OPENAI_API_KEY"),
        }
    }
}

# Memory search and storage settings
DEFAULT_SEARCH_LIMIT = 5
MAX_SEARCH_LIMIT = 10
MEMORY_RELEVANCE_THRESHOLD = 0.7
