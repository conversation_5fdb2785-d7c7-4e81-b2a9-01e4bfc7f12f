"""
Memory manager for handling short-term and long-term memory using Qdrant with OpenAI embeddings.
"""
import asyncio
import datetime
import uuid
from typing import Dict, List, Any, Optional

# Use OpenAI embeddings directly instead of langmem.utils
from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.http import models

from app.logging_custom_directory.logger_custom import logger
from app.memory.langmem_config import (
    QDRANT_URL,
    QDRANT_COLLECTION_NAME,
    MEMORY_EMBEDDING_MODEL,
    MAX_MEMORIES_PER_CHAT,
    ENABLE_TIME_WEIGHTING,
    TIME_DECAY_RATE,
    TIME_WEIGHT_FACTOR,
)

# Initialize OpenAI client for embeddings
openai_client = OpenAI()

# Fallback memory store for when Qdrant is unavailable
class InMemoryFallbackStore:
    """Simple in-memory fallback store for when Qdrant is unavailable."""

    def __init__(self):
        self.memories = []
        self.next_id = 1

    def store_memory(self, content: str, metadata: Dict[str, Any], embeddings: List[float], messages: List[Dict[str, Any]] = None) -> str:
        """Store a memory in the fallback store with sliding window enforcement."""
        memory_id = str(self.next_id)
        self.next_id += 1

        # Ensure metadata has last_accessed_at timestamp
        if "last_accessed_at" not in metadata:
            metadata["last_accessed_at"] = metadata.get("timestamp", datetime.datetime.now().isoformat())

        memory = {
            "id": memory_id,
            "content": content,
            "metadata": metadata,
            "embeddings": embeddings,
            "messages": messages or [],  # Include full conversation
            "timestamp": datetime.datetime.now().isoformat()
        }

        # Enforce sliding window for this chat_id
        chat_id = metadata.get("chat_id")
        user_id = metadata.get("user_id")
        if chat_id and user_id:
            self._enforce_sliding_window_fallback(user_id, chat_id)

        self.memories.append(memory)
        return memory_id

    def _enforce_sliding_window_fallback(self, user_id: str, chat_id: str):
        """Enforce sliding window for fallback store - keep only MAX_MEMORIES_PER_CHAT memories per chat."""
        # Get memories for this specific chat
        chat_memories = [
            m for m in self.memories
            if m["metadata"].get("user_id") == user_id and m["metadata"].get("chat_id") == chat_id
        ]

        # If we have too many memories, remove the oldest ones
        if len(chat_memories) >= MAX_MEMORIES_PER_CHAT:
            # Sort by timestamp to find oldest
            chat_memories.sort(key=lambda x: x["timestamp"])

            # Calculate how many to remove
            to_remove = len(chat_memories) - MAX_MEMORIES_PER_CHAT + 1

            # Remove oldest memories
            for i in range(to_remove):
                oldest_memory = chat_memories[i]
                if oldest_memory in self.memories:
                    self.memories.remove(oldest_memory)
                    logger.info(f"Removed oldest memory {oldest_memory['id']} for chat {chat_id} (sliding window)")

    def search_memories(self, query_embeddings: List[float], user_id: str,
                       chat_id: Optional[str] = None, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories in the fallback store using simple text matching.

        Note: query_embeddings parameter is kept for interface compatibility but not used
        in the fallback store since it uses simple timestamp-based ordering.
        """
        # Filter by user_id and optionally chat_id
        filtered_memories = [
            m for m in self.memories
            if m["metadata"].get("user_id") == user_id and
            (chat_id is None or m["metadata"].get("chat_id") == chat_id)
        ]

        # Sort by timestamp (most recent first) and limit to sliding window size
        filtered_memories.sort(key=lambda x: x["timestamp"], reverse=True)

        # If chat_id is specified, limit to MAX_MEMORIES_PER_CHAT for that chat
        if chat_id:
            filtered_memories = filtered_memories[:MAX_MEMORIES_PER_CHAT]

        return filtered_memories[:limit]

# Global fallback store instance
fallback_store = InMemoryFallbackStore()

# Initialize the memory store
def get_memory_store():
    """Get the memory store - either Qdrant or fallback."""
    try:
        # Use Qdrant for vector storage
        client = QdrantClient(url=QDRANT_URL)
        logger.info(f"Initialized Qdrant memory store at {QDRANT_URL}")
        return client
    except Exception as e:
        logger.error(f"Error initializing Qdrant memory store: {e}")
        # Return None to indicate Qdrant is unavailable
        logger.warning("Qdrant unavailable, will use fallback store")
        return None

# Helper function to get embeddings
def get_embeddings(text: str) -> List[float]:
    """
    Get embeddings for text using OpenAI's embedding API.

    Args:
        text: The text to embed

    Returns:
        List of embedding values
    """
    try:
        response = openai_client.embeddings.create(
            model=MEMORY_EMBEDDING_MODEL,
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error getting embeddings: {e}")
        raise

def calculate_time_weight(timestamp: str, current_time: Optional[datetime.datetime] = None) -> float:
    """
    Calculate time weight factor based on how long ago the memory was created/accessed.

    Args:
        timestamp: ISO format timestamp string
        current_time: Current time (for testing purposes, defaults to now)

    Returns:
        Time weight factor between 0 and 1
    """
    try:
        if not ENABLE_TIME_WEIGHTING:
            return 1.0

        if current_time is None:
            current_time = datetime.datetime.now()

        # Parse the timestamp
        memory_time = datetime.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        # Remove timezone info for comparison if present
        if memory_time.tzinfo is not None:
            memory_time = memory_time.replace(tzinfo=None)
        if current_time.tzinfo is not None:
            current_time = current_time.replace(tzinfo=None)

        # Calculate hours passed
        time_diff = current_time - memory_time
        hours_passed = time_diff.total_seconds() / 3600.0

        # Apply exponential decay: weight = exp(-decay_rate * hours_passed)
        # This follows LangChain's formula: (1.0 - decay_rate) ^ hours_passed
        # But we'll use the more standard exponential decay for better control
        import math
        time_weight = math.exp(-TIME_DECAY_RATE * hours_passed)

        # Ensure weight is between 0 and 1
        return max(0.0, min(1.0, time_weight))

    except Exception as e:
        logger.warning(f"Error calculating time weight for timestamp {timestamp}: {e}")
        return 1.0  # Default to full weight if calculation fails

def calculate_combined_score(semantic_score: float, timestamp: str, current_time: Optional[datetime.datetime] = None) -> float:
    """
    Calculate combined score using semantic similarity and time weighting.

    Args:
        semantic_score: Original semantic similarity score
        timestamp: ISO format timestamp string
        current_time: Current time (for testing purposes)

    Returns:
        Combined score incorporating both semantic and temporal factors
    """
    if not ENABLE_TIME_WEIGHTING:
        return semantic_score

    time_weight = calculate_time_weight(timestamp, current_time)

    # Combined score: weighted average of semantic score and time weight
    # TIME_WEIGHT_FACTOR controls the balance:
    # 0.0 = only semantic similarity
    # 1.0 = only time weighting
    # 0.5 = equal balance
    combined_score = (
        (1.0 - TIME_WEIGHT_FACTOR) * semantic_score +
        TIME_WEIGHT_FACTOR * time_weight
    )

    return combined_score

def update_memory_access_time(memory_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update the last_accessed_at timestamp for a memory result.
    This follows LangChain's approach of updating access time when memories are retrieved.

    Args:
        memory_result: Memory result dictionary

    Returns:
        Updated memory result with current access time
    """
    try:
        # Update the metadata timestamp
        if "metadata" in memory_result:
            memory_result["metadata"]["last_accessed_at"] = datetime.datetime.now().isoformat()

        return memory_result
    except Exception as e:
        logger.warning(f"Error updating memory access time: {e}")
        return memory_result

async def get_chat_memory_count(client, user_id: str, chat_id: str) -> int:
    """
    Get the count of memories for a specific chat.

    Args:
        client: Qdrant client
        user_id: The user ID
        chat_id: The chat ID

    Returns:
        Number of memories for the chat
    """
    try:
        if not client:
            return 0

        # Count memories for this chat
        search_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.user_id",
                    match=models.MatchValue(value=user_id)
                ),
                models.FieldCondition(
                    key="metadata.chat_id",
                    match=models.MatchValue(value=chat_id)
                )
            ]
        )

        # Use scroll to count all memories (limit set high to get all)
        result = client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=search_filter,
            limit=1000,  # High limit to get all memories
            with_payload=False,
            with_vectors=False
        )

        return len(result[0]) if result and result[0] else 0

    except Exception as e:
        logger.error(f"Error counting chat memories: {e}")
        return 0

async def delete_oldest_memories(client, user_id: str, chat_id: str, count_to_delete: int) -> bool:
    """
    Delete the oldest memories for a specific chat.

    Args:
        client: Qdrant client
        user_id: The user ID
        chat_id: The chat ID
        count_to_delete: Number of oldest memories to delete

    Returns:
        Success status
    """
    try:
        if not client or count_to_delete <= 0:
            return False

        # Get all memories for this chat, sorted by timestamp
        search_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.user_id",
                    match=models.MatchValue(value=user_id)
                ),
                models.FieldCondition(
                    key="metadata.chat_id",
                    match=models.MatchValue(value=chat_id)
                )
            ]
        )

        # Scroll to get all memories with timestamps
        result = client.scroll(
            collection_name=QDRANT_COLLECTION_NAME,
            scroll_filter=search_filter,
            limit=1000,  # High limit to get all memories
            with_payload=True,
            with_vectors=False
        )

        if not result or not result[0]:
            return False

        memories = result[0]

        # Sort by timestamp (oldest first)
        memories.sort(key=lambda x: x.payload.get("metadata", {}).get("timestamp", ""))

        # Delete the oldest memories
        points_to_delete = []
        for i in range(min(count_to_delete, len(memories))):
            points_to_delete.append(memories[i].id)

        if points_to_delete:
            client.delete(
                collection_name=QDRANT_COLLECTION_NAME,
                points_selector=models.PointIdsList(points=points_to_delete)
            )
            logger.info(f"Deleted {len(points_to_delete)} oldest memories for chat {chat_id} (sliding window)")

        return True

    except Exception as e:
        logger.error(f"Error deleting oldest memories: {e}")
        return False

async def enforce_sliding_window(client, user_id: str, chat_id: str) -> bool:
    """
    Enforce sliding window by maintaining only MAX_MEMORIES_PER_CHAT memories per chat.

    Args:
        client: Qdrant client
        user_id: The user ID
        chat_id: The chat ID

    Returns:
        Success status
    """
    try:
        # Get current memory count for this chat
        current_count = await get_chat_memory_count(client, user_id, chat_id)

        # If we're at or above the limit, delete oldest memories
        if current_count >= MAX_MEMORIES_PER_CHAT:
            memories_to_delete = current_count - MAX_MEMORIES_PER_CHAT + 1
            success = await delete_oldest_memories(client, user_id, chat_id, memories_to_delete)
            if success:
                logger.info(f"Enforced sliding window for chat {chat_id}: deleted {memories_to_delete} oldest memories")
            return success

        return True

    except Exception as e:
        logger.error(f"Error enforcing sliding window: {e}")
        return False

async def store_user_message(
    user_id: str,
    chat_id: str,
    messages: List[Dict[str, Any]],
    run_async: bool = True
) -> bool:
    """
    Store a user message in memory.

    Args:
        user_id: The user ID
        chat_id: The chat ID
        messages: The message objects to store
        run_async: Whether to run the storage operation asynchronously

    Returns:
        Success status
    """
    async def _store():
        try:
            # Get the Qdrant client
            client = get_memory_store()

            # Format messages for storage
            formatted_messages = []
            for msg in messages:
                if isinstance(msg, dict) and "role" in msg and "content" in msg:
                    formatted_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif isinstance(msg, list) and len(msg) >= 2:
                    formatted_messages.append({"role": "user", "content": msg[0]})
                    formatted_messages.append({"role": "assistant", "content": msg[1]})

            # Extract the last user message for embedding
            last_user_message = None
            for msg in reversed(formatted_messages):
                if msg["role"] == "user":
                    last_user_message = msg["content"]
                    break

            if not last_user_message:
                logger.warning("No user message found to store")
                return False

            # Get embeddings for the message
            embeddings = get_embeddings(last_user_message)

            # Create metadata with both creation and access timestamps
            current_time = datetime.datetime.now().isoformat()
            metadata = {
                "user_id": user_id,
                "chat_id": chat_id,
                "timestamp": current_time,
                "last_accessed_at": current_time,  # Initialize access time to creation time
                "memory_type": "conversation"
            }

            if client:
                # Enforce sliding window before storing new message
                try:
                    await enforce_sliding_window(client, user_id, chat_id)
                except Exception as e:
                    logger.warning(f"Error enforcing sliding window: {e}, continuing with storage")

                # Store in Qdrant
                try:
                    # Create payload
                    payload = {
                        "content": last_user_message,
                        "metadata": metadata,
                        "messages": formatted_messages
                    }

                    client.upsert(
                        collection_name=QDRANT_COLLECTION_NAME,
                        points=[
                            models.PointStruct(
                                id=str(uuid.uuid4()),  # Use UUID for point ID
                                vector=embeddings,
                                payload=payload
                            )
                        ]
                    )

                    logger.info(f"Stored message in Qdrant for user {user_id}, chat {chat_id}")
                    return True
                except Exception as e:
                    logger.error(f"Error storing in Qdrant: {e}, falling back to in-memory store")
                    # Fall through to fallback storage

            # Use fallback storage
            memory_id = fallback_store.store_memory(
                content=last_user_message,
                metadata=metadata,
                embeddings=embeddings,
                messages=formatted_messages
            )
            logger.info(f"Stored message in fallback store for user {user_id}, chat {chat_id}, memory_id: {memory_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing user message: {e}")
            return False

    if run_async:
        # Run asynchronously and return immediately
        asyncio.create_task(_store())
        return True
    else:
        # Run synchronously and wait for result
        return await _store()

async def search_memories(
    user_id: str,
    query: str,
    limit: int = 5,
    include_conversation_history: bool = True,
    chat_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search memories for a user.

    Args:
        user_id: The user ID
        query: The search query
        limit: Maximum number of results to return
        include_conversation_history: Whether to include conversation history
        chat_id: The chat ID (required if include_conversation_history is True)

    Returns:
        Dictionary with search results
    """
    try:
        # Get the memory store (Qdrant client)
        client = get_memory_store()

        # Get embeddings for the query
        embeddings = get_embeddings(query)

        formatted_results = []

        if client:
            # Search in Qdrant
            try:
                # Search for memories in Qdrant
                search_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.user_id",
                            match=models.MatchValue(value=user_id)
                        )
                    ]
                )

                # Add chat_id filter if provided
                if chat_id and include_conversation_history:
                    search_filter.must.append(
                        models.FieldCondition(
                            key="metadata.chat_id",
                            match=models.MatchValue(value=chat_id)
                        )
                    )

                # Search Qdrant with higher limit to allow for sliding window filtering
                search_limit = MAX_MEMORIES_PER_CHAT if chat_id and include_conversation_history else limit
                search_results = client.search(
                    collection_name=QDRANT_COLLECTION_NAME,
                    query_vector=embeddings,
                    limit=search_limit,
                    query_filter=search_filter
                )

                # Format results and apply time-weighted scoring
                for result in search_results:
                    payload = result.payload or {}
                    memory_content = payload.get("content", "")
                    metadata = payload.get("metadata", {})
                    messages = payload.get("messages", [])

                    # Calculate time-weighted score
                    semantic_score = result.score
                    timestamp = metadata.get("last_accessed_at") or metadata.get("timestamp", "")
                    combined_score = calculate_combined_score(semantic_score, timestamp)

                    # Update access time (following LangChain's approach)
                    updated_metadata = metadata.copy()
                    updated_metadata["last_accessed_at"] = datetime.datetime.now().isoformat()

                    formatted_result = {
                        "memory_id": str(result.id),
                        "memory": memory_content,
                        "metadata": updated_metadata,
                        "messages": messages,  # Include full conversation
                        "score": combined_score,  # Use combined score
                        "original_score": semantic_score,  # Keep original for debugging
                        "time_weight": calculate_time_weight(timestamp) if ENABLE_TIME_WEIGHTING else 1.0
                    }

                    formatted_results.append(formatted_result)

                # Sort by combined score (highest first) instead of just timestamp
                formatted_results.sort(key=lambda x: x["score"], reverse=True)

                # Limit to sliding window if chat_id specified
                if chat_id and include_conversation_history:
                    formatted_results = formatted_results[:MAX_MEMORIES_PER_CHAT]

                # Apply final limit
                formatted_results = formatted_results[:limit]

                logger.info(f"Found {len(formatted_results)} memories in Qdrant for user {user_id}")

            except Exception as e:
                logger.error(f"Error searching Qdrant: {e}, falling back to in-memory store")
                # Fall through to fallback search

        # If Qdrant failed or is unavailable, search fallback store
        if not formatted_results:
            fallback_results = fallback_store.search_memories(
                query_embeddings=embeddings,
                user_id=user_id,
                chat_id=chat_id if include_conversation_history else None,
                limit=limit
            )

            for result in fallback_results:
                # Apply time-weighted scoring to fallback results too
                metadata = result["metadata"]
                timestamp = metadata.get("last_accessed_at") or metadata.get("timestamp", "")
                base_score = 0.8  # Default semantic score for fallback
                combined_score = calculate_combined_score(base_score, timestamp)

                # Update access time
                updated_metadata = metadata.copy()
                updated_metadata["last_accessed_at"] = datetime.datetime.now().isoformat()

                formatted_results.append({
                    "memory_id": result["id"],
                    "memory": result["content"],
                    "metadata": updated_metadata,
                    "messages": result.get("messages", []),  # Include full conversation
                    "score": combined_score,  # Use combined score
                    "original_score": base_score,  # Keep original for debugging
                    "time_weight": calculate_time_weight(timestamp) if ENABLE_TIME_WEIGHTING else 1.0
                })

            # Sort fallback results by combined score too
            formatted_results.sort(key=lambda x: x["score"], reverse=True)

            logger.info(f"Found {len(formatted_results)} memories in fallback store for user {user_id}")

        return {"results": formatted_results}
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        return {"results": []}

def format_memory_context(memory_results: Dict[str, Any], include_answers: bool = True, include_header: bool = True) -> str:
    """
    Format memory search results into a readable context string.

    Args:
        memory_results: Results from search_memories function
        include_answers: Whether to include assistant answers in the context
        include_header: Whether to include the "Relevant information" header

    Returns:
        Formatted memory context string
    """
    if not memory_results or not memory_results.get("results"):
        return ""

    memory_context = "\n\nRelevant information from previous conversations:\n" if include_header else ""

    for i, memory in enumerate(memory_results["results"], 1):
        # Get the user question (always available)
        user_question = memory.get('memory', '')

        if include_answers:
            # Try to get the full conversation from the stored messages
            try:
                messages = memory.get('messages', [])
                if messages and len(messages) >= 2:
                    # Find the user question and assistant answer
                    user_msg = None
                    assistant_msg = None

                    for msg in messages:
                        if isinstance(msg, dict):
                            if msg.get('role') == 'user' and not user_msg:
                                user_msg = msg.get('content', '')
                            elif msg.get('role') == 'assistant' and not assistant_msg:
                                assistant_msg = msg.get('content', '')

                    if user_msg and assistant_msg:
                        memory_context += f"{i}. Q: {user_msg}\n"
                        # Truncate long answers to keep context manageable
                        if len(assistant_msg) > 200:
                            assistant_msg = assistant_msg[:200] + "..."
                        memory_context += f"   A: {assistant_msg}\n"
                    else:
                        # Fallback to just the question
                        memory_context += f"{i}. {user_question}\n"
                else:
                    # No messages available, just show the question
                    memory_context += f"{i}. {user_question}\n"

            except Exception as e:
                logger.error(f"Error formatting memory with answers: {e}")
                # Fallback to just showing the question
                memory_context += f"{i}. {user_question}\n"
        else:
            # Just show the user question (current behavior)
            memory_context += f"{i}. {user_question}\n"

    return memory_context
