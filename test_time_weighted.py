"""
Simple test script for time-weighted retrieval functionality.
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.memory_manager import (
    store_user_message, search_memories, calculate_time_weight, calculate_combined_score
)
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import (
    ENABLE_TIME_WEIGHTING, TIME_DECAY_RATE, TIME_WEIGHT_FACTOR
)

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_time_weighted_simple():
    """
    Simple test for time-weighted retrieval functionality.
    """
    print(f"Testing Time-Weighted Retrieval")
    print(f"ENABLE_TIME_WEIGHTING: {ENABLE_TIME_WEIGHTING}")
    print(f"TIME_DECAY_RATE: {TIME_DECAY_RATE}")
    print(f"TIME_WEIGHT_FACTOR: {TIME_WEIGHT_FACTOR}")
    print("-" * 50)
    
    # Test time weight calculation directly
    print(f"\n1. Testing time weight calculation:")
    current_time = datetime.datetime.now()
    
    test_intervals = [
        ("Just now", current_time),
        ("1 hour ago", current_time - datetime.timedelta(hours=1)),
        ("6 hours ago", current_time - datetime.timedelta(hours=6)),
        ("24 hours ago", current_time - datetime.timedelta(hours=24)),
        ("1 week ago", current_time - datetime.timedelta(days=7))
    ]
    
    for label, test_time in test_intervals:
        timestamp = test_time.isoformat()
        time_weight = calculate_time_weight(timestamp, current_time)
        print(f"  {label:12}: time_weight = {time_weight:.4f}")
    
    # Test combined score calculation
    print(f"\n2. Testing combined score calculation:")
    semantic_score = 0.8
    for label, test_time in test_intervals:
        timestamp = test_time.isoformat()
        combined_score = calculate_combined_score(semantic_score, timestamp, current_time)
        time_weight = calculate_time_weight(timestamp, current_time)
        print(f"  {label:12}: semantic={semantic_score:.3f}, time={time_weight:.3f}, combined={combined_score:.3f}")
    
    # Test with actual memory storage and retrieval
    print(f"\n3. Testing with actual memory storage:")
    
    # Initialize Qdrant collection
    init_qdrant_collection()
    
    user_id = "test_time_user"
    chat_id = f"test_time_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # Store a few test messages
    test_messages = [
        "What is the current price of AAPL stock?",
        "Tell me about Tesla's recent performance",
        "What are the best dividend stocks to buy?"
    ]
    
    print(f"Storing {len(test_messages)} test messages...")
    for i, question in enumerate(test_messages):
        messages = [
            {"role": "user", "content": question},
            {"role": "assistant", "content": f"Response to question {i+1}"}
        ]
        
        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False
        )
        
        if success:
            print(f"  ✓ Stored: {question[:40]}...")
        else:
            print(f"  ✗ Failed: {question[:40]}...")
        
        # Small delay to create different timestamps
        await asyncio.sleep(0.5)
    
    # Search and check results
    print(f"\n4. Testing search with time weighting:")
    search_results = await search_memories(
        user_id=user_id,
        query="stock price",
        limit=5,
        include_conversation_history=True,
        chat_id=chat_id
    )
    
    results = search_results.get("results", [])
    print(f"Found {len(results)} results:")
    
    for i, result in enumerate(results):
        memory_content = result.get("memory", "")
        combined_score = result.get("score", 0)
        original_score = result.get("original_score", 0)
        time_weight = result.get("time_weight", 1.0)
        
        print(f"  {i+1}. Score: {combined_score:.3f} (semantic: {original_score:.3f}, time: {time_weight:.3f})")
        print(f"     Content: {memory_content[:50]}...")
    
    # Verify time weighting is working
    if len(results) > 1 and ENABLE_TIME_WEIGHTING:
        score_differences = [abs(r.get("score", 0) - r.get("original_score", 0)) for r in results]
        if any(diff > 0.001 for diff in score_differences):
            print(f"\n✓ Time weighting is affecting scores (differences found)")
        else:
            print(f"\n⚠ Time weighting doesn't seem to be affecting scores significantly")
    
    print(f"\nTime-weighted retrieval test completed!")

if __name__ == "__main__":
    asyncio.run(test_time_weighted_simple())
